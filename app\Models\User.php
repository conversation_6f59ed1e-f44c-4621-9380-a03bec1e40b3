<?php

namespace App\Models;

use Filament\Facades\Filament;
use Filament\Models\Contracts\FilamentUser;
use Filament\Models\Contracts\HasTenants;
use Filament\Panel;
use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;
use Laravel\Sanctum\HasApiTokens;
use MixCode\FilamentMulti2fa\Enums\TwoFactorAuthType;
use MixCode\FilamentMulti2fa\Traits\UsingTwoFA;
use Spatie\Permission\Traits\HasRoles;

/**
 * 
 *
 * @property int $id
 * @property int|null $team_id
 * @property string $name
 * @property string $email
 * @property string|null $mobile
 * @property \Illuminate\Support\Carbon|null $email_verified_at
 * @property string $password
 * @property int|null $role_id
 * @property \Illuminate\Support\Carbon|null $last_seen
 * @property bool $is_active
 * @property string|null $remember_token
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Notifications\DatabaseNotificationCollection<int, \Illuminate\Notifications\DatabaseNotification> $notifications
 * @property-read int|null $notifications_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Spatie\Permission\Models\Permission> $permissions
 * @property-read int|null $permissions_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Spatie\Permission\Models\Role> $roles
 * @property-read int|null $roles_count
 * @property-read \App\Models\Team|null $team
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Laravel\Sanctum\PersonalAccessToken> $tokens
 * @property-read int|null $tokens_count
 * @method static \Database\Factories\UserFactory factory($count = null, $state = [])
 * @method static Builder<static>|User newModelQuery()
 * @method static Builder<static>|User newQuery()
 * @method static Builder<static>|User permission($permissions, $without = false)
 * @method static Builder<static>|User query()
 * @method static Builder<static>|User role($roles, $guard = null, $without = false)
 * @method static Builder<static>|User whereCreatedAt($value)
 * @method static Builder<static>|User whereEmail($value)
 * @method static Builder<static>|User whereEmailVerifiedAt($value)
 * @method static Builder<static>|User whereId($value)
 * @method static Builder<static>|User whereIsActive($value)
 * @method static Builder<static>|User whereLastSeen($value)
 * @method static Builder<static>|User whereMobile($value)
 * @method static Builder<static>|User whereName($value)
 * @method static Builder<static>|User wherePassword($value)
 * @method static Builder<static>|User whereRememberToken($value)
 * @method static Builder<static>|User whereRoleId($value)
 * @method static Builder<static>|User whereTeamId($value)
 * @method static Builder<static>|User whereUpdatedAt($value)
 * @method static Builder<static>|User withoutPermission($permissions)
 * @method static Builder<static>|User withoutRole($roles, $guard = null)
 * @mixin \Eloquent
 */
class User extends Authenticatable implements FilamentUser, HasTenants, MustVerifyEmail
{
    use HasRoles;

    use HasApiTokens;
    use HasFactory;
    use Notifiable;
    use UsingTwoFA;

    protected $guarded = [
        'two_factor_type',
        'two_factor_secret',
        'two_factor_recovery_codes',
        'two_factor_sent_at',
        'two_factor_expires_at',
        'two_factor_confirmed_at',
    ];

    // Temporarily disabled to fix 500 error - will implement alternative approach
    // protected static function boot()
    // {
    //     parent::boot();
    //     // Team scoping logic here
    // }

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'email',
        'mobile',
        'password',
        'role_id',
        'last_seen',
        'is_active',
        'team_id',
        // Social authentication fields
        'provider',
        'provider_id',
        'provider_token',
        'provider_refresh_token',
        'avatar',
        // Phone authentication fields
        'phone',
        'phone_verified_at',
    ];

    /**
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
        'provider_token',
        'provider_refresh_token',
    ];

    /**
     * @var array<string, string>
     */
    // protected $casts = [
    //     'email_verified_at' => 'datetime',
    // ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'phone_verified_at' => 'datetime',
            'password' => 'hashed',
            'last_seen' => 'datetime',
            'is_active' => 'boolean',
            'social_accounts' => 'array',
            'social_accounts_updated_at' => 'datetime',
            'two_factor_type' => TwoFactorAuthType::class,
            'two_factor_sent_at' => 'datetime',
            'two_factor_expires_at' => 'datetime',
            'two_factor_confirmed_at' => 'datetime',
        ];
    }
    
    /**
     * Redirection after OTP verification
     */
    public function redirectAfterVerifyUrl(): ?string
    {
        return route('homepage');
    }

    /**
     * Generate QR code SVG for two-factor authentication
     */
    public function twoFactorQrCodeSvg(): string
    {
        if (!$this->two_factor_secret) {
            return '';
        }

        $google2fa = app('pragmarx.google2fa');
        $qrCodeUrl = $google2fa->getQRCodeUrl(
            config('app.name'),
            $this->email,
            decrypt($this->two_factor_secret)
        );

        // Generate QR code SVG using a simple QR code library
        // For production, you might want to use a more robust QR code library
        return '<div class="p-4 bg-white rounded-lg border text-center">
                    <p class="text-sm text-gray-600 mb-2">Scan this QR code with your authenticator app</p>
                    <div class="inline-block p-4 bg-gray-100 rounded">
                        <p class="text-xs font-mono break-all">' . $qrCodeUrl . '</p>
                    </div>
                    <p class="text-xs text-gray-500 mt-2">Or enter this code manually: ' . decrypt($this->two_factor_secret) . '</p>
                </div>';
    }

    /**
     * Get recovery codes
     */
    public function recoveryCodes(): ?array
    {
        if (!$this->two_factor_recovery_codes) {
            return null;
        }

        return json_decode(decrypt($this->two_factor_recovery_codes), true);
    }

    /**
     * Get the user's profile
     */
    public function profile()
    {
        return $this->hasOne(UserProfile::class);
    }

    /**
     * Get or create user profile
     */
    public function getOrCreateProfile()
    {
        return $this->profile ?: $this->profile()->create([]);
    }

    /**
     * Check if user needs to complete profile
     */
    public function needsProfileCompletion(): bool
    {
        $profile = $this->profile;

        if (!$profile) {
            return true;
        }

        return !$profile->isProfileComplete();
    }

    /**
     * Get user's primary role
     */
    public function getPrimaryRole(): ?string
    {
        return $this->roles->first()?->name;
    }

    /**
     * Check if user has specific role
     */
    public function hasRole(string $role): bool
    {
        return $this->roles->contains('name', $role);
    }

    /**
     * Get dashboard route based on user role
     */
    public function getDashboardRoute(): string
    {
        $role = $this->getPrimaryRole();

        return match ($role) {
            'super_admin' => '/backend',
            'school' => '/app/school-dashboard',
            'teacher' => '/app/teacher-dashboard',
            'parent' => '/app/parent-dashboard',
            'student' => '/app/student-dashboard',
            default => '/app/dashboard',
        };
    }

    /**
     * Get connected social accounts
     */
    public function getConnectedSocialAccounts(): array
    {
        return $this->social_accounts ?? [];
    }

    /**
     * Check if a social provider is connected
     */
    public function hasSocialAccount(string $provider): bool
    {
        $accounts = $this->getConnectedSocialAccounts();
        return isset($accounts[$provider]);
    }

    /**
     * Add or update a social account connection
     */
    public function addSocialAccount(string $provider, array $data): void
    {
        $accounts = $this->getConnectedSocialAccounts();
        $accounts[$provider] = [
            'provider_id' => $data['id'],
            'email' => $data['email'] ?? null,
            'name' => $data['name'] ?? null,
            'avatar' => $data['avatar'] ?? null,
            'connected_at' => now()->toISOString(),
        ];

        $this->update([
            'social_accounts' => $accounts,
            'social_accounts_updated_at' => now(),
        ]);
    }

    /**
     * Remove a social account connection
     */
    public function removeSocialAccount(string $provider): void
    {
        $accounts = $this->getConnectedSocialAccounts();
        unset($accounts[$provider]);

        $this->update([
            'social_accounts' => $accounts,
            'social_accounts_updated_at' => now(),
        ]);
    }

    /**
     * Get available social providers based on .env configuration
     */
    public static function getAvailableSocialProviders(): array
    {
        $providers = [];

        if (config('services.social_auth.providers.google.enabled', false)) {
            $providers['google'] = [
                'name' => 'Google',
                'icon' => 'fab fa-google',
                'color' => '#db4437',
            ];
        }

        if (config('services.social_auth.providers.microsoft.enabled', false)) {
            $providers['microsoft'] = [
                'name' => 'Microsoft',
                'icon' => 'fab fa-microsoft',
                'color' => '#00a1f1',
            ];
        }

        if (config('services.social_auth.providers.apple.enabled', false)) {
            $providers['apple'] = [
                'name' => 'Apple',
                'icon' => 'fab fa-apple',
                'color' => '#000000',
            ];
        }

        if (config('services.social_auth.providers.line.enabled', false)) {
            $providers['line'] = [
                'name' => 'LINE',
                'icon' => 'fab fa-line',
                'color' => '#00c300',
            ];
        }

        return $providers;
    }

    public function canAccessPanel(Panel $panel): bool
    {
        return true;
    }

    public function canAccessTenant(Model $tenant): bool
    {
        // Super admins (team_id = null) can access any tenant
        if ($this->team_id === null && $this->hasRole('super_admin')) {
            return true;
        }

        // Regular users can only access their own team
        return $this->team_id === $tenant->id;
    }

    /** @return Collection<int,Team> */
    public function getTenants(Panel $panel): Collection
    {
        // Super admins (team_id = null) can access all active teams
        if ($this->team_id === null && $this->hasRole('super_admin')) {
            $teams = Team::where('is_active', true)->orderBy('name')->get();

            // If no teams exist, create a default one for super admin access
            if ($teams->isEmpty()) {
                $defaultTeam = Team::firstOrCreate([
                    'slug' => 'default-team'
                ], [
                    'name' => 'Default Team',
                    'description' => 'Default team for super admin access',
                    'is_active' => true,
                ]);
                return collect([$defaultTeam]);
            }

            return $teams;
        }

        // Regular users can ONLY access their assigned team
        if ($this->team && $this->team->is_active) {
            return collect([$this->team]);
        }

        // No access if user has no team or team is inactive
        return collect();
    }

    public function team(): BelongsTo
    {
        return $this->belongsTo(Team::class);
    }

    /**
     * Get assignments created by this user (teacher)
     */
    public function assignmentsCreated(): HasMany
    {
        return $this->hasMany(Assignment::class, 'user_id');
    }

    /**
     * Get assignment submissions by this user (student)
     */
    public function assignmentSubmissions(): HasMany
    {
        return $this->hasMany(AssignmentSubmission::class, 'user_id');
    }

    /**
     * Get assignment submissions graded by this user (teacher)
     */
    public function assignmentSubmissionsGraded(): HasMany
    {
        return $this->hasMany(AssignmentSubmission::class, 'graded_by');
    }
}
