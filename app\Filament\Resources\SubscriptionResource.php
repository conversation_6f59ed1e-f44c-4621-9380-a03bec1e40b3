<?php

namespace App\Filament\Resources;

use App\Filament\Resources\SubscriptionResource\Pages;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use TomatoPHP\FilamentSubscriptions\Models\Subscription;
use Filament\Facades\Filament;

class SubscriptionResource extends Resource
{
    protected static ?string $model = Subscription::class;

    protected static ?string $navigationIcon = 'heroicon-o-credit-card';

    protected static ?string $navigationGroup = 'Billing';

    protected static ?int $navigationSort = 2;

    // Configure tenant relationship for subscriptions
    protected static ?string $tenantOwnershipRelationshipName = 'subscriber';

    public static function isScopedToTenant(): bool
    {
        return true;
    }

    public static function getEloquentQuery(): Builder
    {
        $query = parent::getEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);

        // Apply team filtering based on current tenant
        if (Filament::hasTenancy() && Filament::getTenant()) {
            $query->whereHasMorph('subscriber', ['App\Models\User'], function (Builder $query) {
                $query->where('team_id', Filament::getTenant()->id);
            });
        }

        return $query;
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Subscription Details')
                    ->schema([
                        Forms\Components\Select::make('subscriber_id')
                            ->label('Subscriber')
                            ->relationship('subscriber', 'name')
                            ->searchable()
                            ->preload()
                            ->required()
                            ->columnSpan(2),

                        Forms\Components\Select::make('plan_id')
                            ->label('Plan')
                            ->relationship('plan', 'name')
                            ->searchable()
                            ->preload()
                            ->required()
                            ->columnSpan(2),

                        Forms\Components\TextInput::make('name')
                            ->required()
                            ->maxLength(255)
                            ->columnSpan(2),

                        Forms\Components\TextInput::make('slug')
                            ->required()
                            ->maxLength(255)
                            ->unique(ignoreRecord: true)
                            ->columnSpan(2),

                        Forms\Components\Textarea::make('description')
                            ->columnSpanFull(),

                        Forms\Components\TextInput::make('timezone')
                            ->maxLength(255)
                            ->columnSpan(2),
                    ])
                    ->columns(4),

                Forms\Components\Section::make('Dates')
                    ->schema([
                        Forms\Components\DateTimePicker::make('trial_ends_at')
                            ->label('Trial Ends At')
                            ->columnSpan(2),

                        Forms\Components\DateTimePicker::make('starts_at')
                            ->label('Starts At')
                            ->columnSpan(2),

                        Forms\Components\DateTimePicker::make('ends_at')
                            ->label('Ends At')
                            ->columnSpan(2),

                        Forms\Components\DateTimePicker::make('cancels_at')
                            ->label('Cancels At')
                            ->columnSpan(2),

                        Forms\Components\DateTimePicker::make('canceled_at')
                            ->label('Canceled At')
                            ->columnSpan(2),
                    ])
                    ->columns(4),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('subscriber.name')
                    ->label('Subscriber')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('plan.name')
                    ->label('Plan')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('name')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('starts_at')
                    ->label('Started')
                    ->dateTime()
                    ->sortable(),

                Tables\Columns\TextColumn::make('ends_at')
                    ->label('Ends')
                    ->dateTime()
                    ->sortable(),

                Tables\Columns\BadgeColumn::make('status')
                    ->getStateUsing(function ($record) {
                        if ($record->canceled_at) {
                            return 'Canceled';
                        }
                        if ($record->ends_at && $record->ends_at->isPast()) {
                            return 'Expired';
                        }
                        if ($record->trial_ends_at && $record->trial_ends_at->isFuture()) {
                            return 'Trial';
                        }
                        return 'Active';
                    })
                    ->colors([
                        'success' => 'Active',
                        'warning' => 'Trial',
                        'danger' => ['Canceled', 'Expired'],
                    ]),

                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\TrashedFilter::make(),
                
                Tables\Filters\SelectFilter::make('plan')
                    ->relationship('plan', 'name'),

                Tables\Filters\Filter::make('active')
                    ->query(fn (Builder $query): Builder => $query->where('ends_at', '>', now())->whereNull('canceled_at'))
                    ->label('Active Subscriptions'),

                Tables\Filters\Filter::make('expired')
                    ->query(fn (Builder $query): Builder => $query->where('ends_at', '<', now()))
                    ->label('Expired Subscriptions'),

                Tables\Filters\Filter::make('canceled')
                    ->query(fn (Builder $query): Builder => $query->whereNotNull('canceled_at'))
                    ->label('Canceled Subscriptions'),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\ForceDeleteBulkAction::make(),
                    Tables\Actions\RestoreBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListSubscriptions::route('/'),
            'create' => Pages\CreateSubscription::route('/create'),
            'view' => Pages\ViewSubscription::route('/{record}'),
            'edit' => Pages\EditSubscription::route('/{record}/edit'),
        ];
    }
}
