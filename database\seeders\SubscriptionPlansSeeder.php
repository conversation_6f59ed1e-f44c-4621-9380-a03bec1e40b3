<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use TomatoPHP\FilamentSubscriptions\Models\Plan;

class SubscriptionPlansSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $plans = [
            [
                'name' => ['en' => 'Free'],
                'slug' => 'free',
                'description' => ['en' => 'Basic features for individual users'],
                'price' => 0.00,
                'currency' => 'USD',
                'trial_period' => 0,
                'trial_interval' => 'day',
                'invoice_period' => 1,
                'invoice_interval' => 'month',
                'sort_order' => 1,
                'is_active' => true,
                'features' => [
                    'Access to basic courses',
                    'Limited live classes',
                    'Basic support',
                ],
            ],
            [
                'name' => ['en' => 'Basic'],
                'slug' => 'basic',
                'description' => ['en' => 'Perfect for students and individual learners'],
                'price' => 9.99,
                'currency' => 'USD',
                'trial_period' => 7,
                'trial_interval' => 'day',
                'invoice_period' => 1,
                'invoice_interval' => 'month',
                'sort_order' => 2,
                'is_active' => true,
                'features' => [
                    'Access to all courses',
                    'Unlimited live classes',
                    'Download materials',
                    'Email support',
                ],
            ],
            [
                'name' => ['en' => 'Pro'],
                'slug' => 'pro',
                'description' => ['en' => 'Advanced features for serious learners'],
                'price' => 19.99,
                'currency' => 'USD',
                'trial_period' => 14,
                'trial_interval' => 'day',
                'invoice_period' => 1,
                'invoice_interval' => 'month',
                'sort_order' => 3,
                'is_active' => true,
                'features' => [
                    'Everything in Basic',
                    'Priority support',
                    'Advanced analytics',
                    'Custom learning paths',
                    'Offline access',
                ],
            ],
            [
                'name' => ['en' => 'School'],
                'slug' => 'school',
                'description' => ['en' => 'Complete solution for educational institutions'],
                'price' => 99.99,
                'currency' => 'USD',
                'trial_period' => 30,
                'trial_interval' => 'day',
                'invoice_period' => 1,
                'invoice_interval' => 'month',
                'sort_order' => 4,
                'is_active' => true,
                'features' => [
                    'Everything in Pro',
                    'Multi-user management',
                    'Advanced reporting',
                    'Custom branding',
                    'API access',
                    'Dedicated support',
                ],
            ],
            [
                'name' => ['en' => 'Basic Annual'],
                'slug' => 'basic-annual',
                'description' => ['en' => 'Basic plan with annual billing (2 months free)'],
                'price' => 99.99,
                'currency' => 'USD',
                'trial_period' => 7,
                'trial_interval' => 'day',
                'invoice_period' => 12,
                'invoice_interval' => 'month',
                'sort_order' => 5,
                'is_active' => true,
                'features' => [
                    'Everything in Basic',
                    '2 months free',
                    'Annual billing',
                ],
            ],
            [
                'name' => ['en' => 'Pro Annual'],
                'slug' => 'pro-annual',
                'description' => ['en' => 'Pro plan with annual billing (2 months free)'],
                'price' => 199.99,
                'currency' => 'USD',
                'trial_period' => 14,
                'trial_interval' => 'day',
                'invoice_period' => 12,
                'invoice_interval' => 'month',
                'sort_order' => 6,
                'is_active' => true,
                'features' => [
                    'Everything in Pro',
                    '2 months free',
                    'Annual billing',
                ],
            ],
        ];

        foreach ($plans as $planData) {
            $features = $planData['features'];
            unset($planData['features']);

            $plan = Plan::updateOrCreate(
                ['slug' => $planData['slug']],
                $planData
            );

            // Store features as JSON in description or a separate field
            $currentDescription = $planData['description']['en'];
            $plan->update([
                'description' => ['en' => $currentDescription . "\n\nFeatures:\n• " . implode("\n• ", $features)]
            ]);
        }
    }
}
